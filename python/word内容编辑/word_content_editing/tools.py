"""
Word内容编辑工具 - 提供段落、标题、表格、图片等内容编辑功能
"""
import os
import json
from typing import List, Optional
from docx import Document
from docx.shared import Inches, Pt

from .utils import (
    check_file_writeable,
    ensure_docx_extension,
    find_and_replace_text,
    ensure_heading_style,
    ensure_table_style,
    insert_header_near_text,
    insert_line_or_paragraph_near_text,
    insert_numbered_list_near_text,
    get_paragraph_text,
    find_text
)


def delete_paragraph(filename: str, paragraph_index: int) -> str:
    """
    从文档中删除段落

    Args:
        filename: Word文档路径
        paragraph_index: 要删除的段落索引（从0开始）
    """
    filename = ensure_docx_extension(filename)

    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"

    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"

    try:
        doc = Document(filename)

        # 验证段落索引
        if paragraph_index < 0 or paragraph_index >= len(doc.paragraphs):
            return f"无效的段落索引。文档有 {len(doc.paragraphs)} 个段落 (0-{len(doc.paragraphs)-1})。"

        # 删除段落（通过移除其内容并设置为空）
        # 注意：python-docx不支持真正的段落删除，这是一个变通方法
        paragraph = doc.paragraphs[paragraph_index]
        p = paragraph._p
        p.getparent().remove(p)

        doc.save(filename)
        return f"索引 {paragraph_index} 处的段落已成功删除。"
    except Exception as e:
        return f"删除段落失败: {str(e)}"


def search_and_replace(filename: str, find_text: str, replace_text: str) -> str:
    """
    搜索文本并替换所有出现的地方

    Args:
        filename: Word文档路径
        find_text: 要搜索的文本
        replace_text: 要替换的文本
    """
    filename = ensure_docx_extension(filename)

    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"

    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"

    try:
        doc = Document(filename)

        # 执行查找和替换
        count = find_and_replace_text(doc, find_text, replace_text)

        if count > 0:
            doc.save(filename)
            return f"已将 '{find_text}' 替换为 '{replace_text}'，共 {count} 处。"
        else:
            return f"未找到 '{find_text}'。"
    except Exception as e:
        return f"搜索和替换失败: {str(e)}"


def insert_header_near_text_tool(filename: str, target_text: Optional[str] = None, header_title: Optional[str] = None,
                                      position: str = 'after', header_style: str = 'Heading 1',
                                      target_paragraph_index: Optional[int] = None) -> str:
    """在指定文本附近插入标题（使用指定样式）。通过文本或段落索引指定。"""
    filename = ensure_docx_extension(filename)

    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"

    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"

    return insert_header_near_text(filename, target_text, header_title, position, header_style, target_paragraph_index)


def insert_line_or_paragraph_near_text_tool(filename: str, target_text: Optional[str] = None, line_text: Optional[str] = None,
                                                 position: str = 'after', line_style: Optional[str] = None,
                                                 target_paragraph_index: Optional[int] = None) -> str:
    """
    在指定文本附近插入新行或段落（使用指定或匹配的样式）。通过文本或段落索引指定。
    """
    filename = ensure_docx_extension(filename)

    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"

    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"

    return insert_line_or_paragraph_near_text(filename, target_text, line_text, position, line_style, target_paragraph_index)


def insert_numbered_list_near_text_tool(filename: str, target_text: Optional[str] = None, list_items: Optional[list] = None,
                                             position: str = 'after', target_paragraph_index: Optional[int] = None) -> str:
    """在指定文本附近插入编号列表。通过文本或段落索引指定。"""
    filename = ensure_docx_extension(filename)

    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"

    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"

    return insert_numbered_list_near_text(filename, target_text, list_items, position, target_paragraph_index)


def get_paragraph_text_from_document(filename: str, paragraph_index: int) -> str:
    """
    获取Word文档中特定段落的文本

    Args:
        filename: Word文档路径
        paragraph_index: 段落索引（从0开始）
    """
    filename = ensure_docx_extension(filename)

    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"

    if paragraph_index < 0:
        return "无效参数: paragraph_index必须是非负整数"

    try:
        result = get_paragraph_text(filename, paragraph_index)
        return json.dumps(result, indent=2, ensure_ascii=False)
    except Exception as e:
        return f"获取段落文本失败: {str(e)}"


def find_text_in_document(filename: str, text_to_find: str, match_case: bool = True, whole_word: bool = False) -> str:
    """
    在Word文档中查找指定文本的出现位置

    Args:
        filename: Word文档路径
        text_to_find: 要在文档中搜索的文本
        match_case: 是否区分大小写（True）或忽略大小写（False）
        whole_word: 是否只匹配完整单词（True）或子字符串（False）
    """
    filename = ensure_docx_extension(filename)

    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"

    if not text_to_find:
        return "搜索文本不能为空"

    try:
        result = find_text(filename, text_to_find, match_case, whole_word)
        return json.dumps(result, indent=2, ensure_ascii=False)
    except Exception as e:
        return f"搜索文本失败: {str(e)}"
